{".class": "MypyFile", "_fullname": "app.app", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "BASE_URL": {".class": "SymbolTableNode", "cross_ref": "app.config.BASE_URL", "kind": "Gdef"}, "CORSMiddleware": {".class": "SymbolTableNode", "cross_ref": "starlette.middleware.cors.CORSMiddleware", "kind": "Gdef"}, "CSRFProtection": {".class": "SymbolTableNode", "cross_ref": "app.security.CSRFProtection", "kind": "Gdef"}, "DatastarStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "datastar_py.starlette.DatastarStreamingResponse", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef"}, "FastAPI": {".class": "SymbolTableNode", "cross_ref": "fastapi.applications.FastAPI", "kind": "Gdef"}, "Form": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Form", "kind": "Gdef"}, "InputSanitizer": {".class": "SymbolTableNode", "cross_ref": "app.security.InputSanitizer", "kind": "Gdef"}, "Jinja2Blocks": {".class": "SymbolTableNode", "cross_ref": "jinja2_fragments.fastapi.Jinja2Blocks", "kind": "Gdef"}, "Limiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.app.Limiter", "name": "Limiter", "type": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": null, "type_of_any": 3}}}, "MockLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.app.MockLimiter", "name": "MockLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.app.MockLimiter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.app", "mro": ["app.app.MockLimiter", "builtins.object"], "names": {".class": "SymbolTable", "limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rate_limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.app.MockLimiter.limit", "name": "limit", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.app.MockLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.app.MockLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RateLimitExceeded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.app.RateLimitExceeded", "name": "RateLimitExceeded", "type": {".class": "AnyType", "missing_import_name": "app.app.RateLimitExceeded", "source_any": null, "type_of_any": 3}}}, "RedirectResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.RedirectResponse", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "SECRET": {".class": "SymbolTableNode", "cross_ref": "app.config.SECRET", "kind": "Gdef"}, "SSE": {".class": "SymbolTableNode", "cross_ref": "datastar_py.sse.ServerSentEventGenerator", "kind": "Gdef"}, "SecurityLogger": {".class": "SymbolTableNode", "cross_ref": "app.security.SecurityLogger", "kind": "Gdef"}, "SecurityValidator": {".class": "SymbolTableNode", "cross_ref": "app.security.SecurityValidator", "kind": "Gdef"}, "SessionMiddleware": {".class": "SymbolTableNode", "cross_ref": "starlette.middleware.sessions.SessionMiddleware", "kind": "Gdef"}, "StaticFiles": {".class": "SymbolTableNode", "cross_ref": "starlette.staticfiles.StaticFiles", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.db.User", "kind": "Gdef"}, "UserCreate": {".class": "SymbolTableNode", "cross_ref": "app.schemas.UserCreate", "kind": "Gdef"}, "UserRead": {".class": "SymbolTableNode", "cross_ref": "app.schemas.UserRead", "kind": "Gdef"}, "UserUpdate": {".class": "SymbolTableNode", "cross_ref": "app.schemas.UserUpdate", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.app.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_rate_limit_exceeded_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.app._rate_limit_exceeded_handler", "name": "_rate_limit_exceeded_handler", "type": {".class": "AnyType", "missing_import_name": "app.app._rate_limit_exceeded_handler", "source_any": null, "type_of_any": 3}}}, "add_customer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.add_customer", "name": "add_customer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_customer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.add_customer", "name": "add_customer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_customer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_customer_to_user_new": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.add_customer_to_user_new", "kind": "Gdef"}, "add_security_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "call_next"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.add_security_headers", "name": "add_security_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "call_next"], "arg_types": ["starlette.requests.Request", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_security_headers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.add_security_headers", "name": "add_security_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "call_next"], "arg_types": ["starlette.requests.Request", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_security_headers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app.app", "name": "app", "type": "fastapi.applications.FastAPI"}}, "app_logger": {".class": "SymbolTableNode", "cross_ref": "app.logging_config.app_logger", "kind": "Gdef"}, "asynccontextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.asynccontextmanager", "kind": "Gdef"}, "auth_backend": {".class": "SymbolTableNode", "cross_ref": "app.users.auth_backend", "kind": "Gdef"}, "authenticated_route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.authenticated_route", "name": "authenticated_route", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticated_route", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.authenticated_route", "name": "authenticated_route", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticated_route", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "calc01_router": {".class": "SymbolTableNode", "cross_ref": "app.calc01.router", "kind": "Gdef"}, "check_customer_name_exists": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.check_customer_name_exists", "kind": "Gdef"}, "create_db_and_tables": {".class": "SymbolTableNode", "cross_ref": "app.db.create_db_and_tables", "kind": "Gdef"}, "create_user_settings_db_and_tables": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.create_user_settings_db_and_tables", "kind": "Gdef"}, "create_user_settings_entry": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.create_user_settings_entry", "kind": "Gdef"}, "current_user": {".class": "SymbolTableNode", "cross_ref": "app.users.current_user", "kind": "Gdef"}, "delete_customer": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.delete_customer", "kind": "Gdef"}, "fastapi_users": {".class": "SymbolTableNode", "cross_ref": "app.users.fastapi_users", "kind": "Gdef"}, "forgotpassword_reset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["request", "token", "forgotpass", "forgotpassrepeat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.forgotpassword_reset", "name": "forgotpassword_reset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["request", "token", "forgotpass", "forgotpassrepeat"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpassword_reset", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.forgotpassword_reset", "name": "forgotpassword_reset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["request", "token", "forgotpass", "forgotpassrepeat"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpassword_reset", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forgotpassword_sendemail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["request", "forgotpasswordemail", "user_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.forgotpassword_sendemail", "name": "forgotpassword_sendemail", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["request", "forgotpasswordemail", "user_manager"], "arg_types": ["starlette.requests.Request", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpassword_sendemail", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.forgotpassword_sendemail", "name": "forgotpassword_sendemail", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["request", "forgotpasswordemail", "user_manager"], "arg_types": ["starlette.requests.Request", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpassword_sendemail", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forgotpasswordchange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.forgotpasswordchange", "name": "forgotpasswordchange", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "token"], "arg_types": ["starlette.requests.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpasswordchange", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.forgotpasswordchange", "name": "forgotpasswordchange", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "token"], "arg_types": ["starlette.requests.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpasswordchange", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forgotpasswordemailform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.forgotpasswordemailform", "name": "forgotpasswordemailform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpasswordemailform", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.forgotpasswordemailform", "name": "forgotpasswordemailform", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forgotpasswordemailform", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_csrf_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.app.get_csrf_token", "name": "get_csrf_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_csrf_token", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_remote_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.app.get_remote_address", "name": "get_remote_address", "type": {".class": "AnyType", "missing_import_name": "app.app.get_remote_address", "source_any": null, "type_of_any": 3}}}, "get_user_customers": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.get_user_customers", "kind": "Gdef"}, "get_user_manager": {".class": "SymbolTableNode", "cross_ref": "app.users.get_user_manager", "kind": "Gdef"}, "get_user_settings": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.get_user_settings", "kind": "Gdef"}, "handle_customers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.handle_customers", "name": "handle_customers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_customers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.handle_customers", "name": "handle_customers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_customers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef"}, "is_development": {".class": "SymbolTableNode", "cross_ref": "app.config.is_development", "kind": "Gdef"}, "jinja_partials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.app.jinja_partials", "name": "jinja_partials", "type": {".class": "AnyType", "missing_import_name": "app.app.jinja_partials", "source_any": null, "type_of_any": 3}}}, "lifespan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_app"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_decorated"], "fullname": "app.app.lifespan", "name": "lifespan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_app"], "arg_types": ["fastapi.applications.FastAPI"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lifespan", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.lifespan", "name": "lifespan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_app"], "arg_types": ["fastapi.applications.FastAPI"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lifespan", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._AsyncGeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "limiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app.limiter", "name": "limiter", "type": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "login": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.login", "name": "login", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "login", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.login", "name": "login", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "login", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "login_validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["request", "loginemail", "loginpass", "csrf_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.login_validate", "name": "login_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["request", "loginemail", "loginpass", "csrf_token"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "login_validate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.login_validate", "name": "login_validate", "type": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "logout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.logout", "name": "logout", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.logout", "name": "logout", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "origins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app.origins", "name": "origins", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "read_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.read_root", "name": "read_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_root", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.read_root", "name": "read_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_root", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rendered_html_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "app.app.rendered_html_str", "name": "rendered_html_str", "type": "builtins.str"}}, "security_config": {".class": "SymbolTableNode", "cross_ref": "app.security.security_config", "kind": "Gdef"}, "security_logger": {".class": "SymbolTableNode", "cross_ref": "app.logging_config.security_logger", "kind": "Gdef"}, "security_monitor": {".class": "SymbolTableNode", "cross_ref": "app.logging_config.security_monitor", "kind": "Gdef"}, "signup_form": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.app.signup_form", "name": "signup_form", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signup_form", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.signup_form", "name": "signup_form", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["starlette.requests.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signup_form", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signup_validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["request", "signupemail", "signuppass", "signuppassrepeat", "csrf_token", "user_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.signup_validate", "name": "signup_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["request", "signupemail", "signuppass", "signuppassrepeat", "csrf_token", "user_manager"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signup_validate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.signup_validate", "name": "signup_validate", "type": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "startup_event": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.startup_event", "name": "startup_event", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.startup_event", "name": "startup_event", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startup_event", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "templates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app.templates", "name": "templates", "type": "jinja2_fragments.fastapi.Jinja2Blocks"}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "update_customer": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.update_customer", "kind": "Gdef"}, "update_user_settings": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.update_user_settings", "kind": "Gdef"}, "usercustomers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.usercustomers", "name": "usercustomers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "usercustomers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.usercustomers", "name": "usercustomers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "usercustomers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "userinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.userinfo", "name": "userinfo", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userinfo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.userinfo", "name": "userinfo", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userinfo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "userinfo_save": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["request", "userinfoname", "userinfo", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.userinfo_save", "name": "userinfo_save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["request", "userinfoname", "userinfo", "user"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userinfo_save", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.userinfo_save", "name": "userinfo_save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["request", "userinfoname", "userinfo", "user"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.str", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userinfo_save", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "userprojects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.userprojects", "name": "userprojects", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userprojects", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.userprojects", "name": "userprojects", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "user"], "arg_types": ["starlette.requests.Request", "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "userprojects", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_csrf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["request", "csrf_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.app.validate_csrf", "name": "validate_csrf", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["request", "csrf_token"], "arg_types": ["starlette.requests.Request", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_csrf", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_email_submit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.app.verify_email_submit", "name": "verify_email_submit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "token"], "arg_types": ["starlette.requests.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_email_submit", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.app.verify_email_submit", "name": "verify_email_submit", "type": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": {".class": "AnyType", "missing_import_name": "app.app.Limiter", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\app.py"}