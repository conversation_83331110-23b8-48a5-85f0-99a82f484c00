from collections.abc import AsyncGenerator
from typing import List, Optional
import json
from sqlalchemy import String, Text, ForeignKey, Integer
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, relationship, Mapped, mapped_column
from sqlalchemy.types import TypeDecorator, VARCHAR

# Database URL for user settings
USER_SETTINGS_DATABASE_URL = "sqlite+aiosqlite:///./users_settings.db"


class JSONEncodedDict(TypeDecorator):
    """Represents an immutable structure as a json-encoded string."""

    impl = VARCHAR
    cache_ok = True

    def process_bind_param(self, value):
        if value is not None:
            value = json.dumps(value)
        return value

    def process_result_value(self, value):
        if value is not None:
            value = json.loads(value)
        return value


class UserSettingsBase(DeclarativeBase):
    pass


class Customer(UserSettingsBase):
    __tablename__ = "customers"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_email: Mapped[str] = mapped_column(String, ForeignKey("user_settings.email"))
    name: Mapped[str] = mapped_column(String)
    info: Mapped[str] = mapped_column(Text)
    # Relationship back to user settings
    user_settings: Mapped["UserSettings"] = relationship("UserSettings", back_populates="customers")


class UserSettings(UserSettingsBase):
    __tablename__ = "user_settings"
    email: Mapped[str] = mapped_column(String, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String)
    info: Mapped[str] = mapped_column(Text)
    # Relationship to customers
    customers: Mapped[List["Customer"]] = relationship("Customer", back_populates="user_settings", cascade="all, delete-orphan")


# Create async engine for user settings database
user_settings_engine = create_async_engine(USER_SETTINGS_DATABASE_URL)
user_settings_async_session_maker = async_sessionmaker(user_settings_engine, expire_on_commit=False)


async def create_user_settings_db_and_tables():
    """Create the user settings database and tables."""
    async with user_settings_engine.begin() as conn:
        await conn.run_sync(UserSettingsBase.metadata.create_all)


async def get_user_settings_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get an async session for the user settings database."""
    async with user_settings_async_session_maker() as session:
        yield session


async def create_user_settings_entry(email: str, name: str = "", info: str = ""):
    """Create a new user settings entry after successful signup."""
    async with user_settings_async_session_maker() as session:
        # Check if user settings already exist
        existing_settings = await session.get(UserSettings, email)
        if existing_settings:
            return existing_settings

        # Create new user settings
        user_settings = UserSettings(
            email=email,
            name=name,
            info=info
        )

        session.add(user_settings)
        await session.commit()
        await session.refresh(user_settings)
        return user_settings


async def add_customer_to_user(user_email: str, customer_name: str, customer_info: str = ""):
    """Add a customer to a user's settings."""
    async with user_settings_async_session_maker() as session:
        # Check if user settings exist
        user_settings = await session.get(UserSettings, user_email)
        if not user_settings:
            raise ValueError(f"User settings not found for email: {user_email}")

        # Create new customer
        customer = Customer(
            user_email=user_email,
            name=customer_name,
            info=customer_info
        )

        session.add(customer)
        await session.commit()
        await session.refresh(customer)
        return customer


async def get_user_settings_with_customers(user_email: str):
    """Get user settings along with all their customers."""
    from sqlalchemy import select

    async with user_settings_async_session_maker() as session:
        # Get user settings
        user_settings = await session.get(UserSettings, user_email)
        if not user_settings:
            return None

        # Get customers
        stmt = select(Customer).where(Customer.user_email == user_email)
        result = await session.execute(stmt)
        customers = result.scalars().all()

        return {
            "email": user_settings.email,
            "name": user_settings.name,
            "info": user_settings.info,
            "customers": [
                {
                    "id": customer.id,
                    "name": customer.name,
                    "info": customer.info
                }
                for customer in customers
            ]
        }


async def get_user_settings(user_email: str):
    """Get user settings by email."""
    async with user_settings_async_session_maker() as session:
        user_settings = await session.get(UserSettings, user_email)
        if not user_settings:
            # Create default user settings if they don't exist
            return {
                "email": user_email,
                "name": "",
                "info": ""
            }

        return {
            "email": user_settings.email,
            "name": user_settings.name,
            "info": user_settings.info
        }


async def update_user_settings(user_email: str, name: str, info: str):
    """Update user settings."""
    async with user_settings_async_session_maker() as session:
        user_settings = await session.get(UserSettings, user_email)
        if not user_settings:
            # Create new user settings if they don't exist
            user_settings = UserSettings(email=user_email, name=name, info=info)
            session.add(user_settings)
        else:
            # Update existing user settings
            user_settings.name = name
            user_settings.info = info

        await session.commit()
        await session.refresh(user_settings)
        return user_settings


async def get_user_customers(user_email: str):
    """Get all customers for a user."""
    from sqlalchemy import select

    async with user_settings_async_session_maker() as session:
        stmt = select(Customer).where(Customer.user_email == user_email).order_by(Customer.id)
        result = await session.execute(stmt)
        customers = result.scalars().all()

        return [
            {
                "id": customer.id,
                "name": customer.name,
                "info": customer.info
            }
            for customer in customers
        ]


async def update_customer(customer_id: int, name: str, info: str):
    """Update an existing customer."""
    async with user_settings_async_session_maker() as session:
        customer = await session.get(Customer, customer_id)
        if not customer:
            raise ValueError(f"Customer with ID {customer_id} not found")

        customer.name = name
        customer.info = info

        await session.commit()
        await session.refresh(customer)
        return customer


async def delete_customer(customer_id: int):
    """Delete a customer."""
    async with user_settings_async_session_maker() as session:
        customer = await session.get(Customer, customer_id)
        if not customer:
            raise ValueError(f"Customer with ID {customer_id} not found")

        await session.delete(customer)
        await session.commit()
        return True


async def check_customer_name_exists(user_email: str, name: str, exclude_customer_id: Optional[int] = None):
    """Check if a customer name already exists for a user."""
    from sqlalchemy import select

    async with user_settings_async_session_maker() as session:
        stmt = select(Customer).where(
            Customer.user_email == user_email,
            Customer.name == name
        )

        # Exclude current customer when updating
        if exclude_customer_id:
            stmt = stmt.where(Customer.id != exclude_customer_id)

        result = await session.execute(stmt)
        existing_customer = result.scalars().first()

        return existing_customer is not None


async def add_customer_to_user_new(user_email: str, name: str, info: str):
    """Add a new customer to a user (returns the new customer with ID)."""
    async with user_settings_async_session_maker() as session:
        # Check if user settings exist
        user_settings = await session.get(UserSettings, user_email)
        if not user_settings:
            # Create user settings if they don't exist
            user_settings = await create_user_settings_entry(email=user_email, name="", info="")

        # Create new customer
        customer = Customer(
            user_email=user_email,
            name=name,
            info=info
        )

        session.add(customer)
        await session.commit()
        await session.refresh(customer)

        return {
            "id": customer.id,
            "name": customer.name,
            "info": customer.info
        }
