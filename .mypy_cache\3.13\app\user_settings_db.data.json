{".class": "MypyFile", "_fullname": "app.user_settings_db", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "Customer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.user_settings_db.UserSettingsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.user_settings_db.Customer", "name": "Customer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.user_settings_db.Customer", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "metadata": {}, "module_name": "app.user_settings_db", "mro": ["app.user_settings_db.Customer", "app.user_settings_db.UserSettingsBase", "sqlalchemy.orm.decl_api.DeclarativeBase", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.id", "name": "id", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.info", "name": "info", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.name", "name": "name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "user_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.user_email", "name": "user_email", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "user_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.Customer.user_settings", "name": "user_settings", "type": {".class": "Instance", "args": ["app.user_settings_db.UserSettings"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.user_settings_db.Customer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.user_settings_db.Customer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeclarativeBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeBase", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKey", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "JSONEncodedDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeDecorator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.user_settings_db.JSONEncodedDict", "name": "JSONEncodedDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.user_settings_db.JSONEncodedDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.user_settings_db", "mro": ["app.user_settings_db.JSONEncodedDict", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.JSONEncodedDict.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.JSONEncodedDict.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["length", "collation"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["sqlalchemy.sql.sqltypes.VARCHAR"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.sqltypes.VARCHAR", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_bind_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.user_settings_db.JSONEncodedDict.process_bind_param", "name": "process_bind_param", "type": null}}, "process_result_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.user_settings_db.JSONEncodedDict.process_result_value", "name": "process_result_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.user_settings_db.JSONEncodedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.user_settings_db.JSONEncodedDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.Mapped", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Text", "kind": "Gdef"}, "TypeDecorator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeDecorator", "kind": "Gdef"}, "USER_SETTINGS_DATABASE_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.USER_SETTINGS_DATABASE_URL", "name": "USER_SETTINGS_DATABASE_URL", "type": "builtins.str"}}, "UserSettings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.user_settings_db.UserSettingsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.user_settings_db.UserSettings", "name": "UserSettings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.user_settings_db.UserSettings", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "metadata": {}, "module_name": "app.user_settings_db", "mro": ["app.user_settings_db.UserSettings", "app.user_settings_db.UserSettingsBase", "sqlalchemy.orm.decl_api.DeclarativeBase", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.UserSettings.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "customers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.UserSettings.customers", "name": "customers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["app.user_settings_db.Customer"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.UserSettings.email", "name": "email", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.UserSettings.info", "name": "info", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.user_settings_db.UserSettings.name", "name": "name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.user_settings_db.UserSettings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.user_settings_db.UserSettings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserSettingsBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.decl_api.DeclarativeBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.user_settings_db.UserSettingsBase", "name": "UserSettingsBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.user_settings_db.UserSettingsBase", "has_param_spec_type": false, "metaclass_type": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "metadata": {}, "module_name": "app.user_settings_db", "mro": ["app.user_settings_db.UserSettingsBase", "sqlalchemy.orm.decl_api.DeclarativeBase", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.user_settings_db.UserSettingsBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.user_settings_db.UserSettingsBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.user_settings_db.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_customer_to_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["user_email", "customer_name", "customer_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.add_customer_to_user", "name": "add_customer_to_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["user_email", "customer_name", "customer_info"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_customer_to_user", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_customer_to_user_new": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["user_email", "name", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.add_customer_to_user_new", "name": "add_customer_to_user_new", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["user_email", "name", "info"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_customer_to_user_new", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "async_sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker", "kind": "Gdef"}, "check_customer_name_exists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["user_email", "name", "exclude_customer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.check_customer_name_exists", "name": "check_customer_name_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["user_email", "name", "exclude_customer_id"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_customer_name_exists", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_async_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.create_async_engine", "kind": "Gdef"}, "create_user_settings_db_and_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.create_user_settings_db_and_tables", "name": "create_user_settings_db_and_tables", "type": null}}, "create_user_settings_entry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["email", "name", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.create_user_settings_entry", "name": "create_user_settings_entry", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["email", "name", "info"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_user_settings_entry", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_customer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["customer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.delete_customer", "name": "delete_customer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["customer_id"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_customer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_customers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_email"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.get_user_customers", "name": "get_user_customers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["user_email"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_customers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_settings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_email"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.get_user_settings", "name": "get_user_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["user_email"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_settings", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_settings_async_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "app.user_settings_db.get_user_settings_async_session", "name": "get_user_settings_async_session", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_settings_async_session", "ret_type": {".class": "Instance", "args": ["sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_settings_with_customers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_email"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.get_user_settings_with_customers", "name": "get_user_settings_with_customers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["user_email"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_settings_with_customers", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "mapped_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.mapped_column", "kind": "Gdef"}, "relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.relationship", "kind": "Gdef"}, "update_customer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["customer_id", "name", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.update_customer", "name": "update_customer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["customer_id", "name", "info"], "arg_types": ["builtins.int", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_customer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_user_settings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["user_email", "name", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.user_settings_db.update_user_settings", "name": "update_user_settings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["user_email", "name", "info"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_user_settings", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_settings_async_session_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.user_settings_async_session_maker", "name": "user_settings_async_session_maker", "type": {".class": "Instance", "args": ["sqlalchemy.ext.asyncio.session.AsyncSession"], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker"}}}, "user_settings_engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.user_settings_db.user_settings_engine", "name": "user_settings_engine", "type": "sqlalchemy.ext.asyncio.engine.AsyncEngine"}}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\user_settings_db.py"}